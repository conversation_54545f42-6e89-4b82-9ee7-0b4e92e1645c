{"globals": {"Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "HttpRequestConfig": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "computed": true, "createApp": true, "customRef": true, "debounce": true, "deepClone": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "getCurrentInstance": true, "getCurrentScope": true, "globalLogin": true, "h": true, "http": true, "httpRequest": true, "inject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "log": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "printItemInfo": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "removeAllPendingRequest": true, "reportErrorLog": true, "resolveComponent": true, "setRootVariable": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useId": true, "useLink": true, "useModel": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTemplateRef": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true}}