/**
 * HTTP 工具测试文件
 * 用于验证各种请求功能是否正常工作
 */

import { httpRequest } from './http'

// 测试用的 API 接口
export const testApi = {
  /**
   * 测试 GET 请求
   */
  async testGet() {
    console.log('=== 测试 GET 请求 ===')
    try {
      // 基本 GET 请求
      const result1 = await httpRequest.get('/api/test/get', {
        page: 1,
        size: 10
      })
      console.log('基本 GET 请求成功:', result1)

      // 带缓存的 GET 请求
      const result2 = await httpRequest.get('/api/test/cache', undefined, {
        cache: true,
        maxAge: 5000,
        loading: true,
        loadingText: '加载中...'
      })
      console.log('缓存 GET 请求成功:', result2)

    } catch (error) {
      console.error('GET 请求失败:', error)
    }
  },

  /**
   * 测试 POST 请求 (JSON)
   */
  async testPost() {
    console.log('=== 测试 POST 请求 (JSON) ===')
    try {
      const result = await httpRequest.post('/api/test/post', {
        name: '测试用户',
        email: '<EMAIL>',
        age: 25
      }, {
        loading: true,
        successMessage: true,
        retryTimes: 2
      })
      console.log('POST 请求成功:', result)
    } catch (error) {
      console.error('POST 请求失败:', error)
    }
  },

  /**
   * 测试表单提交 (form-data)
   */
  async testFormData() {
    console.log('=== 测试表单提交 (form-data) ===')
    try {
      // 创建一个模拟文件
      const blob = new Blob(['测试文件内容'], { type: 'text/plain' })
      const file = new File([blob], 'test.txt', { type: 'text/plain' })

      const result = await httpRequest.postForm('/api/test/form', {
        name: '表单测试',
        file: file,
        tags: ['测试', '表单']
      }, {
        loading: true,
        loadingText: '提交表单中...'
      })
      console.log('表单提交成功:', result)
    } catch (error) {
      console.error('表单提交失败:', error)
    }
  },

  /**
   * 测试 URL 编码表单提交
   */
  async testUrlEncoded() {
    console.log('=== 测试 URL 编码表单提交 ===')
    try {
      const result = await httpRequest.postUrlEncoded('/api/test/urlencoded', {
        username: 'testuser',
        password: 'testpass',
        remember: true
      }, {
        loading: true
      })
      console.log('URL 编码表单提交成功:', result)
    } catch (error) {
      console.error('URL 编码表单提交失败:', error)
    }
  },

  /**
   * 测试文件上传
   */
  async testUpload() {
    console.log('=== 测试文件上传 ===')
    try {
      // 创建一个模拟图片文件
      const canvas = document.createElement('canvas')
      canvas.width = 100
      canvas.height = 100
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.fillStyle = '#ff0000'
        ctx.fillRect(0, 0, 100, 100)
      }

      canvas.toBlob(async (blob) => {
        if (blob) {
          const file = new File([blob], 'test-image.png', { type: 'image/png' })
          
          try {
            const result = await httpRequest.upload('/api/test/upload', file, {
              userId: 123,
              category: 'avatar'
            }, {
              loading: true,
              loadingText: '上传中...',
              onUploadProgress: (progressEvent) => {
                const progress = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                )
                console.log(`上传进度: ${progress}%`)
              }
            })
            console.log('文件上传成功:', result)
          } catch (error) {
            console.error('文件上传失败:', error)
          }
        }
      }, 'image/png')
    } catch (error) {
      console.error('文件上传测试失败:', error)
    }
  },

  /**
   * 测试文件下载
   */
  async testDownload() {
    console.log('=== 测试文件下载 ===')
    try {
      // 下载指定文件名
      await httpRequest.download('/api/test/download/report', '测试报告.pdf', {
        loading: true,
        loadingText: '下载中...'
      })
      console.log('文件下载成功')
    } catch (error) {
      console.error('文件下载失败:', error)
    }
  },

  /**
   * 测试其他 HTTP 方法
   */
  async testOtherMethods() {
    console.log('=== 测试其他 HTTP 方法 ===')
    try {
      // PUT 请求
      const putResult = await httpRequest.put('/api/test/put/123', {
        name: '更新的名称',
        status: 1
      })
      console.log('PUT 请求成功:', putResult)

      // PATCH 请求
      const patchResult = await httpRequest.patch('/api/test/patch/123', {
        status: 2
      })
      console.log('PATCH 请求成功:', patchResult)

      // DELETE 请求
      const deleteResult = await httpRequest.delete('/api/test/delete/123')
      console.log('DELETE 请求成功:', deleteResult)

    } catch (error) {
      console.error('其他方法请求失败:', error)
    }
  },

  /**
   * 测试自定义请求配置
   */
  async testCustomRequest() {
    console.log('=== 测试自定义请求配置 ===')
    try {
      const result = await httpRequest.request({
        method: 'POST',
        url: '/api/test/custom',
        data: { message: '自定义请求' },
        headers: {
          'Custom-Header': 'custom-value',
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        loading: true,
        retryTimes: 3,
        retryDelay: 2000
      })
      console.log('自定义请求成功:', result)
    } catch (error) {
      console.error('自定义请求失败:', error)
    }
  },

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行 HTTP 工具测试...')
    
    await this.testGet()
    await this.testPost()
    await this.testFormData()
    await this.testUrlEncoded()
    await this.testUpload()
    await this.testDownload()
    await this.testOtherMethods()
    await this.testCustomRequest()
    
    console.log('✅ 所有测试完成！')
  }
}

// 在浏览器控制台中可以运行的测试函数
if (typeof window !== 'undefined') {
  // 将测试函数挂载到 window 对象上，方便在控制台调用
  ;(window as any).httpTest = testApi
  
  console.log('HTTP 测试工具已加载！')
  console.log('在控制台中运行以下命令进行测试：')
  console.log('- httpTest.runAllTests() // 运行所有测试')
  console.log('- httpTest.testGet() // 测试 GET 请求')
  console.log('- httpTest.testPost() // 测试 POST 请求')
  console.log('- httpTest.testFormData() // 测试表单提交')
  console.log('- httpTest.testUpload() // 测试文件上传')
  console.log('- httpTest.testDownload() // 测试文件下载')
}

export default testApi
