<template>
    <div>
        <a-modal
            v-model:open="open"
            :title="title"
            :destroyOnClose="true"
            @ok="handleOk"
            :okText="okText"
            @cancel="cancel"
            :confirmLoading="confirmLoading"
            width="790px"
        >
            <div v-if="state == 1">
                <div class="desc">
                    <p>导入说明：请下载模版，然后按模版填写信息</p>
                    <a :href="templateSrc">
                        <span>下载模版</span>
                        <img src="./download.png" />
                    </a>
                    <!-- <a-button
                        type="link"
                        class="btn-link-color"
                        @click="download"
                        mr-10
                        flex
                        flex-items-center
                        :loading="downloadLoading"
                    >
                        <span>下载模版</span>
                        <DownloadOutlined style="font-size: 16px" />
                    </a-button> -->
                </div>
                <a-upload-dragger :disabled="disabled" :showUploadList="false" :multiple="false" :customRequest="customRequest">
                    <div class="upfile">
                        <img src="./icon-skml-scbg.png" />
                        <p>上传表格</p>
                        <p>也可以直接拖拽表格到此处上传（支持格式：xls、xlsx）</p>
                    </div>
                </a-upload-dragger>
                <div class="fileList">
                    <ul>
                        <li v-for="(item, index) in fileList" :key="index">
                            <div class="li-item" v-if="item.status == 0">
                                <img src="./icon-skml-xe.png" />
                                <div flex-1 pt-4>
                                    <p class="file-name">{{ item.name }}</p>
                                    <span class="file-size">{{ item.size }}MB</span>
                                    <span class="file-success">等待导入</span>
                                    <CloseCircleOutlined
                                        @click="fileList.splice(index, 1)"
                                        class="file-icon color-#9EA5C2 cursor-pointer"
                                    />
                                </div>
                            </div>
                            <div class="li-item" v-if="item.status == 1">
                                <img src="./icon-skml-xe.png" />
                                <div flex-1 pt-4>
                                    <p class="file-name">{{ item.name }}</p>
                                    <a-progress :percent="item.schedule" :showInfo="false" strokeColor="#00B781" size="small" />
                                </div>
                            </div>
                            <div class="li-item" v-if="item.status == 2">
                                <img src="./icon-skml-xe.png" />
                                <div flex-1 pt-4>
                                    <p class="file-name">{{ item.name }}</p>
                                    <span class="file-size">{{ item.size }}MB</span>
                                    <span class="file-success">上传成功</span>
                                    <CheckCircleOutlined class="file-icon color-#00B781" />
                                </div>
                            </div>
                            <div class="li-item" v-if="item.status == 3">
                                <img src="./icon-skml-xe.png" />
                                <div flex-1 pt-4>
                                    <p class="file-name">{{ item.name }}</p>
                                    <span class="file-size">{{ item.size }}MB</span>
                                    <span class="file-error">上传失败</span>
                                </div>
                                <span class="re-upload">重新上传</span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div v-else class="upload-info">
                <div mt-20 mr-24 text-right>
                    <a @click="exportError">
                        <span class="font-size-14 down_color">下载异常数据</span>
                        <img src="./download.png" class="w-16 h-16 ml-8" pos-relative top-2 />
                    </a>
                </div>
                <div class="upload-info-num">
                    <p>
                        正常数据条数：
                        <span class="color-#00B781">{{ msg.successNum }}</span>
                        条
                    </p>
                    <p>
                        异常数据条数：
                        <span class="color-#FE6565">{{ msg.errorCount }}</span>
                        条
                    </p>
                </div>
                <div class="upload-info-tip" v-if="msg.errorCount > 0">
                    <p>异常提示：</p>
                    <ul>
                        <li v-for="(item, index) in msg.errorMsg" :key="index">
                            {{ item }}
                        </li>
                    </ul>
                </div>
            </div>
        </a-modal>
    </div>
</template>
<script setup>
/**
 * 通用模版导入
 * @param {Boolean} show - 控制弹窗显示隐藏.
 * @param {string} code - 模板code.
 * @param {string} title - 弹窗标题.
 * @param {number} importType - 导入类型,后端提供.
 */
import { message } from 'ant-design-vue'
const open = ref(false)
const confirmLoading = ref(false)
const fileList = ref([])
const okText = ref('导入')
const state = ref(1)
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    code: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '导入',
    },
    importType: {
        type: Number,
        default: 2,
    },
    uploadUrl: {
        type: String,
        required: false,
    },
    templateSrc: {
        type: String,
        default: '',
    },
    progressRequest: {
        type: Function,
        required: false,
    },
    errorExport: {
        type: Function,
        required: false,
    },
})
const disabled = ref(false)
const importIdList = ref([])
const msg = reactive({
    errorMsg: [],
    errorCount: 0,
    successNum: 0,
})
const downloadLoading = ref(false)

const emit = defineEmits(['update:show'])
watch(
    () => props.show,
    val => {
        state.value = 1
        fileList.value = []
        open.value = props.show
        disabled.value = false
        okText.value = '导入'
        downloadLoading.value = false
    },
)
const cancel = () => {
    emit('update:show', false)
    emit('cancel')
}
const exportError = () => {
    props.errorExport
        ? props.errorExport(importIdList.value)
        : http.download(
              '/unicard/common/export/importErrorLog',
              {
                  importIds: importIdList.value,
              },
              '异常数据下载',
          )
}
const handleOk = async () => {
    if (okText.value == '重新上传') {
        state.value = 1
        okText.value = '导入'
        disabled.value = false
        fileList.value = []
        importIdList.value = []
        msg.errorMsg = []
        msg.successNum = 0
        msg.errorCount = 0
        return false
    }
    if (disabled.value) {
        state.value = 2
        okText.value = '重新上传'
        return false
    }
    importIdList.value = []
    msg.errorMsg = []
    msg.successNum = 0
    msg.errorCount = 0
    if (fileList.value.length == 0) {
        message.error('请先上传文件')
        return
    }
    confirmLoading.value = true
    disabled.value = true
    for (let i = 0; i < fileList.value.length; i++) {
        const item = fileList.value[i]
        await importItem(item)
    }
    confirmLoading.value = false
    state.value = 2
    okText.value = '重新上传'
}
const importItem = item => {
    return new Promise(async (resolve, reject) => {
        try {
            const { data } = await http.form(props.uploadUrl ?? '/unicard/common/import', {
                importType: props.importType,
                file: item.file,
            })
            importIdList.value.push(data)
            item.status = 1
            const timer = setInterval(async () => {
                const progress = props.progressRequest ? await props.progressRequest(data) : await getProgress(data)
                item.schedule = progress.schedule
                if (progress.schedule == 100) {
                    item.status = 2
                    msg.errorMsg = [...msg.errorMsg, ...progress.errorMsg]
                    msg.errorCount = msg.errorCount + progress.errorCount
                    msg.successNum = msg.successNum + progress.successCount
                    clearInterval(timer)
                    resolve()
                }
            }, 1000)
        } catch (error) {
            item.status = 3
            resolve()
        }
    })
}
const getProgress = async importId => {
    const { data } = await http.post('/unicard/common/import/progress', { importId })
    return data
}
const customRequest = e => {
    const size = (e.file.size / 1024 / 1024).toFixed(2)
    fileList.value.push({ file: e.file, status: 0, name: e.file.name, size })
}

const download = async () => {
    try {
        downloadLoading.value = true
        await http.download('/cloud/common/export/template', { code: props.code }, props.title)
        YMessage.success(`下载模板成功!`)
    } catch (error) {
        YMessage.error(`下载模板失败!`)
    } finally {
        downloadLoading.value = false
    }
}
</script>
<style lang="less" scoped>
.upload-info {
    .down_color {
        color: var(--primary-color);
    }
    .upload-info-num {
        margin: 0 24px;
        background: #f8f8fa;
        border-radius: 4px;
        padding: 20px;
        line-height: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #2c2c2c;
        margin-top: 12px;
        margin-bottom: 20px;
    }
    .upload-info-tip {
        margin: 0 24px;
        max-height: 358px;
        background: #f8f8fa;
        border-radius: 4px;
        overflow-y: auto;
        padding: 20px;
        margin-bottom: 20px;
        p {
            font-size: 14px;
            font-weight: 400;
            color: #262626;
            padding-bottom: 12px;
        }
        li {
            font-size: 14px;
            padding-bottom: 12px;
            font-weight: 400;
            color: #9ea5c2;
        }
        li:nth-last-child(1) {
            padding-bottom: 0;
        }
    }
}
.fileList {
    margin: 0 24px;
    .li-item {
        height: 60px;
        background: #f8f8fa;
        border-radius: 4px;
        margin-bottom: 12px;
        display: flex;
        padding: 15px 20px;
        box-sizing: border-box;
        align-items: center;
        position: relative;
        img {
            width: 30px;
            height: 30px;
            margin-right: 12px;
        }
    }
    .file-size {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        padding-right: 8px;
    }
    .file-name {
        font-size: 14px;
        font-weight: 400;
        line-height: 1;
        color: #2c2c2c;
    }
    .re-upload {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5);
        cursor: pointer;
    }

    .file-icon {
        position: absolute;
        top: 24px;
        right: 24px;
        font-size: 16px;
    }
    .file-error {
        font-size: 12px;
        font-weight: 400;
        color: #ff0a0a;
    }
    .file-success {
        font-size: 12px;
        font-weight: 400;
        color: var(--primary-color);
    }
}
.desc {
    display: flex;
    height: 48px;
    background: #f8f8fa;
    border-radius: 4px;
    margin: 20px 24px;
    align-items: center;
    padding-left: 20px;
    justify-content: space-between;
    a {
        display: flex;
        align-items: center;
        color: var(--primary-color);
        img {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            position: relative;
            top: -1px;
        }
        padding-right: 26px;
    }
}
:deep(.ant-upload) {
    margin: 0 auto;
    width: 742px;
    border: 0;
    margin-bottom: 20px;
    height: 250px;
    background: #f8f8fa;
    border-radius: 4px;
}
.upfile {
    text-align: center;
    padding-top: 74px;
    box-sizing: border-box;
    p:nth-child(2) {
        font-weight: 500;
        color: #2c2c2c;
        font-size: 18px;
        margin-top: 12px;
        margin-bottom: 8px;
    }
}
</style>
