/**
 * HTTP 请求使用示例
 * 展示如何使用封装好的 HTTP 工具进行各种类型的请求
 */

import { httpRequest, HttpRequestConfig } from '@/utils/http'

// ===== 用户相关 API =====
export const userApi = {
  /**
   * 获取用户列表 - GET 请求示例
   * @param params 查询参数
   */
  getUserList: (params?: {
    page?: number
    size?: number
    name?: string
    status?: number
  }) => {
    return httpRequest.get<{
      list: Array<{
        id: number
        name: string
        email: string
        status: number
      }>
      total: number
    }>('/api/users', params, {
      cache: true,        // 启用缓存
      maxAge: 30000,      // 缓存30秒
      loading: true,      // 显示加载动画
      loadingText: '加载用户列表...'
    })
  },

  /**
   * 获取用户详情 - GET 请求示例
   * @param userId 用户ID
   */
  getUserDetail: (userId: number) => {
    return httpRequest.get<{
      id: number
      name: string
      email: string
      avatar: string
      createTime: string
    }>(`/api/users/${userId}`, undefined, {
      cache: true,
      maxAge: 10000  // 缓存10秒
    })
  },

  /**
   * 创建用户 - POST 请求示例 (JSON格式)
   * @param userData 用户数据
   */
  createUser: (userData: {
    name: string
    email: string
    password: string
    phone?: string
  }) => {
    return httpRequest.post<{ id: number }>('/api/users', userData, {
      loading: true,
      loadingText: '创建用户中...',
      successMessage: true,  // 成功后显示提示
      retryTimes: 2,         // 失败重试2次
      retryDelay: 1000       // 重试间隔1秒
    })
  },

  /**
   * 更新用户信息 - PUT 请求示例
   * @param userId 用户ID
   * @param userData 更新的用户数据
   */
  updateUser: (userId: number, userData: {
    name?: string
    email?: string
    phone?: string
  }) => {
    return httpRequest.put(`/api/users/${userId}`, userData, {
      loading: true,
      successMessage: true
    })
  },

  /**
   * 删除用户 - DELETE 请求示例
   * @param userId 用户ID
   */
  deleteUser: (userId: number) => {
    return httpRequest.delete(`/api/users/${userId}`, {
      loading: true,
      successMessage: true
    })
  }
}

// ===== 文件相关 API =====
export const fileApi = {
  /**
   * 上传用户头像 - 文件上传示例
   * @param userId 用户ID
   * @param file 头像文件
   */
  uploadAvatar: (userId: number, file: File) => {
    return httpRequest.upload<{ url: string }>(
      `/api/users/${userId}/avatar`,
      file,
      { type: 'avatar' },  // 额外的表单数据
      {
        loading: true,
        loadingText: '上传头像中...',
        successMessage: true,
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          console.log(`上传进度: ${progress}%`)
        }
      }
    )
  },

  /**
   * 批量上传文件 - 表单提交示例
   * @param files 文件列表
   * @param category 文件分类
   */
  uploadFiles: (files: FileList, category: string) => {
    const formData = {
      category,
      files: Array.from(files)
    }
    
    return httpRequest.postForm<{ urls: string[] }>('/api/files/batch', formData, {
      loading: true,
      loadingText: '批量上传中...',
      successMessage: true
    })
  },

  /**
   * 下载文件 - 文件下载示例
   * @param fileId 文件ID
   * @param filename 保存的文件名
   */
  downloadFile: (fileId: string, filename?: string) => {
    return httpRequest.download(`/api/files/${fileId}/download`, filename, {
      loading: true,
      loadingText: '下载中...'
    })
  },

  /**
   * 导出用户数据 - Excel下载示例
   * @param params 导出参数
   */
  exportUsers: (params?: {
    startDate?: string
    endDate?: string
    status?: number
  }) => {
    return httpRequest.download('/api/users/export', '用户数据.xlsx', {
      loading: true,
      loadingText: '导出中...',
      // 可以传递查询参数
      params
    })
  }
}

// ===== 认证相关 API =====
export const authApi = {
  /**
   * 用户登录 - 表单提交示例
   * @param credentials 登录凭据
   */
  login: (credentials: {
    username: string
    password: string
    captcha?: string
  }) => {
    // 使用 URL 编码格式提交
    return httpRequest.postUrlEncoded<{
      token: string
      refreshToken: string
      userInfo: {
        id: number
        name: string
        email: string
      }
    }>('/api/auth/login', credentials, {
      loading: true,
      loadingText: '登录中...'
    })
  },

  /**
   * 刷新Token - 自定义请求头示例
   */
  refreshToken: () => {
    const refreshToken = localStorage.getItem('refresh_token')
    
    return httpRequest.post<{
      token: string
      refreshToken: string
    }>('/api/auth/refresh', {}, {
      headers: {
        'Authorization': `Bearer ${refreshToken}`,
        'Content-Type': 'application/json'
      }
    })
  }
}

// ===== 高级用法示例 =====
export const advancedApi = {
  /**
   * 自定义请求配置示例
   * @param data 请求数据
   */
  customRequest: (data: any) => {
    const config: HttpRequestConfig = {
      method: 'POST',
      url: '/api/custom',
      data,
      headers: {
        'Custom-Header': 'custom-value',
        'Content-Type': 'application/xml'  // 覆盖默认的JSON格式
      },
      timeout: 10000,
      loading: true,
      retryTimes: 3,
      retryDelay: 2000
    }
    
    return httpRequest.request(config)
  },

  /**
   * 强制更新缓存示例
   * @param userId 用户ID
   */
  forceUpdateUserCache: (userId: number) => {
    return httpRequest.get(`/api/users/${userId}`, undefined, {
      cache: true,
      forceUpdate: true,  // 强制更新缓存
      maxAge: 30000
    })
  },

  /**
   * 无加载动画的静默请求
   * @param data 数据
   */
  silentRequest: (data: any) => {
    return httpRequest.post('/api/silent', data, {
      loading: false,        // 不显示加载动画
      successMessage: false  // 不显示成功提示
    })
  }
}

// ===== 在 Vue 组件中的使用示例 =====
/*
<template>
  <div>
    <button @click="loadUsers">加载用户</button>
    <button @click="createUser">创建用户</button>
    <input type="file" @change="uploadAvatar" accept="image/*" />
    <button @click="exportData">导出数据</button>
  </div>
</template>

<script setup lang="ts">
import { userApi, fileApi } from '@/api/example'

// 加载用户列表
const loadUsers = async () => {
  try {
    const result = await userApi.getUserList({ page: 1, size: 10 })
    console.log('用户列表:', result.list)
    console.log('总数:', result.total)
  } catch (error) {
    console.error('加载失败:', error)
    // 错误提示会自动显示，无需手动处理
  }
}

// 创建用户
const createUser = async () => {
  try {
    const result = await userApi.createUser({
      name: '张三',
      email: '<EMAIL>',
      password: '123456',
      phone: '13800138000'
    })
    console.log('创建成功，用户ID:', result.id)
    // 成功提示会自动显示
  } catch (error) {
    console.error('创建失败:', error)
  }
}

// 上传头像
const uploadAvatar = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    try {
      const result = await fileApi.uploadAvatar(123, file)
      console.log('头像上传成功:', result.url)
    } catch (error) {
      console.error('上传失败:', error)
    }
  }
}

// 导出数据
const exportData = async () => {
  try {
    await fileApi.exportUsers({
      startDate: '2024-01-01',
      endDate: '2024-12-31'
    })
    // 文件会自动下载
  } catch (error) {
    console.error('导出失败:', error)
  }
}
</script>
*/
